# Investment Statistics API Documentation

## Overview

The `getInvestmentStats` function is a comprehensive API endpoint that calculates and returns detailed investment portfolio statistics for authenticated users. This endpoint provides real-time portfolio analysis including profit/loss calculations, investment distribution by type, and performance metrics.

## Endpoint

```
GET /api/investments/stats
```

## Authentication

Requires user authentication. The function uses `req.user.id` to filter investments by the authenticated user.

## Function Flow

### 1. Data Retrieval
- Fetches all investments for the authenticated user from the database
- Filters owned stocks (`purpose: 'OWNED'` and `type: 'STOCK'`) for real-time price analysis

### 2. Real-time Price Fetching
- Extracts stock symbols from owned stocks
- Uses the Stock Strategy pattern to fetch current market prices from external APIs
- Builds a mapping of stock names to current prices

### 3. Investment Enhancement
For each investment, the function:
- **Handles Legacy Data**: Supports both new `purchases` array and legacy `purchasedPrice`/`quantity` fields
- **Calculates Metrics**: Computes average purchase price, total quantity, and profit/loss
- **Adds Current Prices**: Enriches stock data with real-time market prices
- **Computes Profit/Loss**: Calculates per-share and total profit/loss for owned stocks

### 4. Portfolio Statistics Calculation
- **Total Investment**: Sum of all money invested across all owned assets
- **Total Current Value**: Current market value of all owned assets
- **Total Return**: Absolute profit/loss (Current Value - Investment)
- **Return Percentage**: Percentage return on investment

### 5. Data Aggregation
- **Investments by Type**: Groups owned investments by type (STOCK, MUTUAL_FUND, etc.)
- **Recent Investments**: Returns all investments sorted by creation date (newest first)
- **Performance Data**: Individual stock performance metrics for owned stocks

## Response Format

```json
{
  "success": true,
  "data": {
    "totalInvestment": 50000,
    "totalCurrentValue": 55000,
    "totalReturn": 5000,
    "returnPercentage": 10.0,
    "investmentsByType": [
      {
        "name": "STOCK",
        "value": 45000
      },
      {
        "name": "MUTUAL_FUND",
        "value": 10000
      }
    ],
    "recentInvestments": [
      {
        "_id": "...",
        "name": "HDFC Bank",
        "symbol": "HDFCBANK",
        "type": "STOCK",
        "purpose": "OWNED",
        "purchases": [
          {
            "price": 1500,
            "quantity": 10
          }
        ],
        "currentPrice": 1650,
        "profitPerShare": 150,
        "totalProfit": 1500,
        "avgPurchasePrice": 1500,
        "totalQuantity": 10,
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "performanceData": [
      {
        "name": "HDFC Bank",
        "value": 1500
      }
    ]
  }
}
```

## Key Features

### Multi-Purchase Support
- Supports multiple purchase entries per investment
- Calculates weighted average purchase price
- Maintains backward compatibility with legacy single-purchase format

### Real-time Price Integration
- Fetches current market prices for owned stocks
- Uses Strategy pattern for different investment types
- Graceful fallback when price data is unavailable

### Comprehensive Calculations
- **Average Purchase Price**: Weighted average across all purchases
- **Profit per Share**: Current price minus average purchase price
- **Total Profit**: (Current price × Total quantity) - Total investment
- **Portfolio Metrics**: Overall portfolio performance and distribution

### Investment Types Supported
- **STOCK**: Real-time price tracking with profit/loss calculations
- **MUTUAL_FUND**: Investment value tracking
- **GOLD**: Commodity investment tracking
- **SILVER**: Precious metals investment tracking

### Investment Purposes
- **OWNED**: Investments actually owned by the user (included in calculations)
- **MONITORING**: Investments being tracked but not owned (excluded from totals)

## Data Models

### Investment Schema
```typescript
interface Investment {
  _id?: string;
  name: string;
  symbol: string;
  // Legacy fields (backward compatibility)
  purchasedPrice?: number;
  quantity?: number;
  // New multi-purchase support
  purchases?: Purchase[];
  type: INVESTMENT_TYPE;
  purpose: INVESTMENT_PURPOSE;
  targetPrice?: number;
  userId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // Enhanced fields (calculated)
  currentPrice?: number;
  profitPerShare?: number;
  totalProfit?: number;
  avgPurchasePrice?: number;
  totalQuantity?: number;
}
```

### Purchase Schema
```typescript
interface Purchase {
  price: number;
  quantity: number;
}
```

## Error Handling

The function includes comprehensive error handling:
- Database connection errors
- External API failures for price fetching
- Data validation and type checking
- Graceful degradation when price data is unavailable

## Performance Considerations

- Efficient database queries with user-specific filtering
- Batch API calls for stock price fetching
- In-memory calculations for portfolio metrics
- Optimized data structures for aggregations

## Usage in Frontend

This endpoint is consumed by the investment dashboard to display:
- Portfolio overview cards
- Investment distribution charts
- Recent investments list
- Individual stock performance metrics
- Overall portfolio return analysis

## Dependencies

- **MongoDB/Mongoose**: Database operations
- **Investment Strategy Pattern**: External price data fetching
- **Express.js**: HTTP request/response handling
- **Authentication Middleware**: User identification and authorization
