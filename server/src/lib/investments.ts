import { convertDateFormat, convertToStockFormat } from '@/utils/misc';
import axios from 'axios';
import { load as cheerio } from 'cheerio';

interface MutualFundMap {
  [key: string]: number;
}

interface StockMap {
  [key: string]: string;
}

export interface StockData {
  name: string;
  data: [number, number][];
}

interface MutualFundResponse {
  folio: {
    name: string;
    data: [number, number][];
  };
}

export interface StockResponse {
  candles: [number, number][];
}

interface SilverRate {
  date: string;
  silver1g: number;
}

interface CurrencyRateItem {
  time: number;
  value: number;
  source: string;
  target: string;
}

// Investment Type Enum
export enum InvestmentType {
  MUTUAL_FUND = 'mutual',
  STOCK = 'stock',
  GOLD = 'gold',
  SILVER = 'silver',
  CURRENCY = 'currency',
}

// Strategy interface - unified interface for all investment strategies
interface InvestmentStrategy {
  readonly type: string;
  readonly isScraper: boolean;
  fetchData(items?: any): Promise<StockData[]>;
}

// Abstract base class for all investment strategies
abstract class BaseInvestmentStrategy implements InvestmentStrategy {
  abstract readonly type: string;
  abstract readonly isScraper: boolean;
  abstract fetchData(items?: any): Promise<StockData[]>;
}

// Concrete strategy implementations
class MutualFundStrategy extends BaseInvestmentStrategy {
  readonly type = InvestmentType.MUTUAL_FUND;
  readonly isScraper = false;

  buildUrl(id: number): string {
    return `https://groww.in/v1/api/data/mf/web/v1/scheme/${id}/graph?benchmark=false&months=1`;
  }

  formatData(_: string, data: MutualFundResponse): StockData {
    return {
      name: data.folio.name,
      data: data.folio.data,
    };
  }

  async fetchData(items: MutualFundMap): Promise<StockData[]> {
    if (!items || Object.keys(items).length === 0) {
      return [];
    }

    try {
      const responses = await Promise.all(
        Object.entries(items).map(async ([name, id]) => {
          const { data } = await axios.get(this.buildUrl(id));
          return this.formatData(name, data);
        }),
      );
      return responses;
    } catch (error) {
      console.error('Error fetching mutual fund data:', error);
      return [];
    }
  }
}

class StockStrategy extends BaseInvestmentStrategy {
  readonly type = InvestmentType.STOCK;
  readonly isScraper = false;

  buildUrl(id: string): string {
    return `https://groww.in/v1/api/charting_service/v2/chart/delayed/exchange/NSE/segment/CASH/${id}/monthly/v2?intervalInMinutes=30&minimal=true`;
  }

  formatData(name: string, data: StockResponse): StockData {
    return {
      name,
      data: data?.candles.map((k) => [k[0] * 1000, k[1]]) || [],
    };
  }

  async fetchData(items: StockMap): Promise<StockData[]> {
    if (!items || Object.keys(items).length === 0) {
      return [];
    }

    try {
      const responses = await Promise.all(
        Object.entries(items).map(async ([name, id]) => {
          const { data } = await axios.get(this.buildUrl(id));
          return this.formatData(name, data);
        }),
      );
      return responses;
    } catch (error) {
      console.error('Error fetching stock data:', error);
      return [];
    }
  }
}

class GoldStrategy extends BaseInvestmentStrategy {
  readonly type = InvestmentType.GOLD;
  readonly isScraper = true;

  async fetchData(): Promise<StockData[]> {
    const { data } = await axios.get('https://www.tanishq.co.in/gold-rate.html?lang=en_IN');

    const $ = cheerio(data);

    const getArray = (selector: string): string[] => {
      const rawValue = $(selector).attr('value') || '';
      return rawValue
        .replaceAll('[', '')
        .replaceAll(']', '')
        .split(',')
        .map((k) => k.trim());
    };

    const dates = getArray('#goldRateDates');
    // const rates18 = getArray("#goldRate18KT");
    const rates22 = getArray('#goldRate22KT');
    // const rates24 = getArray('#goldRate24KT');

    const sliced = dates
      .map((date, index) => ({
        date: convertDateFormat(date),
        // gold18KT: parseFloat(rates18[index]),
        gold22KT: parseFloat(rates22[index]),
        // gold24KT: parseFloat(rates24[index]),
      }))
      .slice(10);

    return [convertToStockFormat('Gold 22KT', 'gold22KT', sliced)];
  }
}

class SilverStrategy extends BaseInvestmentStrategy {
  readonly type = InvestmentType.SILVER;
  readonly isScraper = true;

  async fetchData(): Promise<StockData[]> {
    const { data } = await axios.get(
      'https://www.thehindubusinessline.com/silver-rate-today/Chennai/',
      {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          accept:
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'accept-encoding': 'gzip, deflate, br, zstd',
          'accept-language': 'en-US,en;q=0.9,ta-IN;q=0.8,ta;q=0.7',
          'cache-control': 'no-cache',
          dnt: '1',
          pragma: 'no-cache',
          priority: 'u=0, i',
          'sec-ch-ua': '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Linux"',
          'sec-fetch-dest': 'document',
          'sec-fetch-mode': 'navigate',
          'sec-fetch-site': 'none',
          'sec-fetch-user': '?1',
          'upgrade-insecure-requests': '1',
        },
      },
    );
    const $ = cheerio(data);
    // Select the third table with the class "table table-balance-sheet"
    const table = $('.table.table-balance-sheet').eq(2);
    const rows = table.find('tbody tr');

    const result: SilverRate[] = [];

    rows.each((_, row) => {
      const cols = $(row).find('td');
      if (cols.length >= 4) {
        // console.log(
        //   parseInt($(cols[1]).text().trim().trim().replace('₹', '').replaceAll(',', '')) / 10,
        // );
        result.push({
          date: $(cols[0]).text().trim(),
          silver1g: parseInt($(cols[1]).text().trim().replace('₹', '').replaceAll(',', '')) / 10,
          // silver100g: $(cols[2]).text().trim(),
          // silver1kg: $(cols[3]).clone().children().remove().end().text().trim(), // remove span and just get price
        });
      }
    });

    return [convertToStockFormat('Silver', 'silver1g', result)];
  }
}

class CurrencyStrategy extends BaseInvestmentStrategy {
  readonly type = InvestmentType.CURRENCY;
  readonly isScraper = true;

  async fetchData(): Promise<StockData[]> {
    try {
      const { data } = await axios.get<CurrencyRateItem[]>(
        'https://wise.com/rates/history+live?source=INR&target=USD&length=30&resolution=daily&unit=day',
        {
          headers: {
            'User-Agent':
              'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          },
        },
      );

      return [
        convertToStockFormat(
          'USD',
          'usdToInr',
          data.map((item) => {
            const date = new Date(item.time).toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            });

            // Format the flipped rate to show 1 USD = x INR with 2 decimal places
            const flippedRate = item.value > 0 ? (1 / item.value).toFixed(2) : '0';

            return {
              date,
              usdToInr: flippedRate,
              inrToUsd: item.value,
              source: item.source,
              target: item.target,
            };
          }),
        ),
      ];
    } catch (error) {
      console.error('Error fetching currency rates:', error);
      return [];
    }
  }
}

// Strategy Factory Function
function getInvestmentStrategy(type: string): InvestmentStrategy | null {
  const investmentType = type.toLowerCase() as InvestmentType;

  switch (investmentType) {
    case InvestmentType.MUTUAL_FUND:
      return new MutualFundStrategy();
    case InvestmentType.STOCK:
      return new StockStrategy();
    case InvestmentType.GOLD:
      return new GoldStrategy();
    case InvestmentType.SILVER:
      return new SilverStrategy();
    case InvestmentType.CURRENCY:
      return new CurrencyStrategy();
    default:
      return null;
  }
}

// Export the factory function and strategy classes
export {
  getInvestmentStrategy,
  MutualFundStrategy,
  StockStrategy,
  GoldStrategy,
  SilverStrategy,
  CurrencyStrategy,
  InvestmentStrategy,
};
